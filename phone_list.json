{"phones": [{"phone": "18157710144", "tokenId": 13, "registeredAt": "2025-06-27T02:39:38.170Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:06.209Z"}, {"phone": "19237282459", "tokenId": 27, "registeredAt": "2025-06-27T02:53:03.871Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:17.728Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19202633847", "tokenId": 28, "registeredAt": "2025-06-27T02:53:27.567Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:18.554Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19237284635", "tokenId": 29, "registeredAt": "2025-06-27T02:55:28.785Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:19.332Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19212542036", "tokenId": 30, "registeredAt": "2025-06-27T02:55:52.181Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:20.157Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19202687941", "tokenId": 31, "registeredAt": "2025-06-27T02:56:19.365Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:20.943Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19237286063", "tokenId": 32, "registeredAt": "2025-06-27T02:56:43.052Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:21.764Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19284431259", "tokenId": 33, "registeredAt": "2025-06-27T02:57:08.445Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:22.540Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19281890924", "tokenId": 34, "registeredAt": "2025-06-27T02:57:24.439Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:24.012Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "18721878530", "tokenId": 35, "registeredAt": "2025-06-27T02:57:48.050Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:24.822Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292398104", "tokenId": 1, "registeredAt": "2025-07-01T10:48:03.817Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:25.766Z"}, {"phone": "19284430421", "tokenId": 2, "registeredAt": "2025-07-01T10:48:37.237Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:26.709Z"}, {"phone": "19272588904", "tokenId": 3, "registeredAt": "2025-07-01T10:49:07.232Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:27.653Z"}, {"phone": "19292457486", "tokenId": 4, "registeredAt": "2025-07-01T10:49:44.925Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:28.588Z"}, {"phone": "15056922415", "tokenId": 5, "registeredAt": "2025-07-01T10:50:15.613Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:29.540Z"}, {"phone": "19016557191", "tokenId": 6, "registeredAt": "2025-07-01T10:50:41.911Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:30.672Z"}, {"phone": "19202535286", "tokenId": 7, "registeredAt": "2025-07-01T10:52:27.184Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:31.741Z"}, {"phone": "19232384458", "tokenId": 8, "registeredAt": "2025-07-01T10:52:58.914Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:32.669Z"}, {"phone": "19292446803", "tokenId": 9, "registeredAt": "2025-07-01T10:53:27.697Z", "tokenValid": true, "lastTokenTest": "2025-07-01T11:35:33.582Z"}], "lastUpdated": "2025-07-02T01:35:43.106Z", "totalCount": 139, "description": "已注册的手机号列表"}